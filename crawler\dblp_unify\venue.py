from typing import Literal

# 会议名称列表
PUB_CONF_A = [
    # ========== 一、计算机体系结构/并行与分布计算/存储系统 ==========
    'ppopp',
    'fast',
    'dac',
    'hpca',
    'micro',
    'sc',
    'asplos',
    'isca',
    'usenix',
    'eurosys',

    # ========== 二、计算机网络 ==========
    'sigcomm',
    'mobicom',
    'infocom',
    'nsdi',

    # ========== 三、网络与信息安全 ==========
    'ccs',
    'eurocrypt',
    'sp',
    'crypto',
    'uss',
    'ndss',

    # ========== 四、软件工程/系统软件/程序设计语言 ==========
    'pldi',
    'popl',
    'sigsoft',
    'sosp',
    'ooplsa',
    'ase',
    'icse',
    'issta',
    'osdi',
    'fm',

    # ========== 五、数据库/数据挖掘/内容检索 ==========
    'sigmod',
    'kdd',
    'icde',
    'sigir',
    # 'vldb', # vldb 不发布到会议中，发表于期刊中

    # ========== 六、人工智能 ==========
    'aaai',
    'neurips',
    'nips',
    'acl',
    'cvpr',
    'iccv',
    'icml',
    'ijcai',

    # ========== 七、计算机科学理论 ==========
    'stoc',
    'soda',
    'cav',
    'focs',
    'lics',

    # ========== 八、计算机图形学与多媒体 ==========
    'mm',
    'siggraph',
    'vr',
    'vis',

    # ========== 九、人机交互与普适计算 ==========
    'cscw',
    'chi',
    'ubicomp',
    'uist',

    # ========== 十、交叉/综合/新兴 ==========
    'www',
    'rtss',
    'wine'
]

# 期刊名称列表
PUB_JOURNALS_A = [
    # ========== 一、计算机体系结构/并行与分布计算/存储系统 ==========
    'tocs',
    'tos',
    'tcad',
    'tc',
    'tpds',
    'taco',

    # ========== 二、计算机网络 ==========
    'jsac',
    'tmc',
    'ton',

    # ========== 三、网络与信息安全 ==========
    'tdsc',
    'tifs',
    'joc',

    # ========== 四、软件工程/系统软件/程序设计语言 ==========
    'toplas',
    'tosem',
    'tse',
    'tsc',

    # ========== 五、数据库/数据挖掘/内容检索 ==========
    'tods',
    'tois',
    'tkde',
    'vldb',  # vldb 只获取期刊结果
]

# A类 DBLP venue 名称特殊映射表
CONF_A_VENUE_MAPPING = {
    # 需要特殊处理的会议名称
    'usenix_atc': 'USENIX ATC',  # USENIX Annual Technical Conference
    'fse': 'SIGSOFT FSE',  # FSE/ESEC
    'esec': 'SIGSOFT FSE',  # FSE/ESEC的另一个名称
    'sp': 'SP',  # IEEE S&P的简化名称
    'usenix_security': 'USENIX Security',
    'uss': 'USENIX Security',  # USENIX Security的简称
    'mm': 'ACM Multimedia',  # ACM MM
    'acm_mm': 'ACM Multimedia',
    'vr': 'IEEE VR',
    'vis': 'IEEE VIS',
    'ieee_vis': 'IEEE VIS',
    'sc': 'SC$',

    # 需要特殊处理的期刊发表会议
    'pldi': 'Proc ACM Program Lang',  # PLDI论文发布在PACMPL期刊
    'popl': 'Proc ACM Program Lang',  # POPL论文发布在PACMPL期刊
    'oopsla': 'Proc ACM Program Lang',  # OOPSLA论文发布在PACMPL期刊
    'sigmod': 'Proc ACM Manag Data',  # SIGMOD主会议论文发布在PACMMOD期刊
    'vldb': 'Proc VLDB Endow',  # VLDB期刊的实际名称
    'cscw': 'Proc ACM Hum Comput Interact',  # CSCW论文发布在PACMHCI期刊
    'ubicomp': 'Proc ACM Interact Mob Wearable Ubiquitous Technol',  # UbiComp论文发布在IMWUT期刊
}





def get_venue_name(conference_key: str, year: int=None) -> str:
    """
    获取会议在DBLP中的venue名称
    """
    # 特殊处理年份相关的venue名称变化
    if year is not None and conference_key.lower() == 'nips':
        return 'NIPS' if year < 2020 else 'NeurIPS'

    # 检查是否在特殊映射表中
    if conference_key.lower() in CONF_A_VENUE_MAPPING:
        return CONF_A_VENUE_MAPPING[conference_key.lower()]

    return conference_key.upper()

def get_all_venue_by_rule(ccf: Literal['a', 'b', 'c'], classification: Literal['conf', 'journal']) -> str:
    """
    根据 CCF等级 和 期刊/会议类型 获取对应的所有 venue 的名称
    """
    if ccf == 'a' and classification == 'conf':
        return PUB_CONF_A
    elif ccf == 'a' and classification == 'journal':
        return PUB_JOURNALS_A
    else:
        raise NotImplementedError(f"CCF {ccf} {classification} not implemented")

def get_ccf_by_venue(venue_name: str) -> str:
    """
    获取会议或期刊的CCF等级
    """
    venue_lower = venue_name.lower()

    # A 类检查
    if venue_lower in PUB_CONF_A or venue_lower in PUB_JOURNALS_A:
        return 'a'

    # TODO: 添加B类和C类的支持
    return 'unknown'

def get_classification_by_venue(venue_name: str) -> str:
    """
    获取会议或期刊的类型: conf or journal
    """
    venue_lower = venue_name.lower()

    # 检查是否为会议
    if venue_lower in PUB_CONF_A:
        return 'conf'
    # 检查是否为期刊
    if venue_lower in PUB_JOURNALS_A:
        return 'journal'

    # 默认返回unknown
    return None